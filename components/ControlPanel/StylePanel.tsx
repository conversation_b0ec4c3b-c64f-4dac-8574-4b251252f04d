import React, { memo, useCallback } from 'react';
import { useDynamicStyleStore } from '../../stores/dynamicStyleStore';
import { useStyleStore } from '../../stores/styleStore';

/**
 * 样式面板组件 - 完整功能版
 * 🎯 核心价值：提供完整的样式控制功能
 * ⚡ 性能优化：使用memo包装和useCallback优化
 * 📊 功能范围：字体、边距、形状、显示模式、主题等全面控制
 * 🔄 重构状态：完整实现版本，支持所有样式控制功能
 */

interface StylePanelProps {
  className?: string;
}

export const StylePanel = memo<StylePanelProps>(({ className = '' }) => {
  // 动态样式状态
  const {
    fontSize,
    matrixMargin,
    cellShape,
    displayMode,
    enableCircleScale,
    circleScaleFactor,
    enableVirtualization,
    showAllNumbers,
    showAllColors,
    showAllLevel1,
    showAllLevel2,
    showAllLevel3,
    showAllLevel4,
    setFontSize,
    setMatrixMargin,
    setCellShape,
    setDisplayMode,
    toggleCircleScale,
    setCircleScaleFactor,
    toggleVirtualization,
    setShowAllNumbers,
    setShowAllColors,
    setShowAllLevel1,
    setShowAllLevel2,
    setShowAllLevel3,
    setShowAllLevel4,
    toggleAllLevels,
    getCellShapes,
    getDisplayModes,
    getShapeIcon,
    getDisplayModeIcon,
    resetToDefaults,
  } = useDynamicStyleStore();

  // 主题样式状态
  const {
    currentTheme,
    setControlPanelTheme,
    resetStyles,
  } = useStyleStore();

  // 字体大小控制
  const handleFontSizeChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const size = parseInt(event.target.value);
    setFontSize(size);
  }, [setFontSize]);

  // 边距控制
  const handleMarginChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const margin = parseInt(event.target.value);
    setMatrixMargin(margin);
  }, [setMatrixMargin]);

  // 圆形缩放因子控制
  const handleScaleFactorChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const factor = parseFloat(event.target.value);
    setCircleScaleFactor(factor);
  }, [setCircleScaleFactor]);

  // 主题切换
  const handleThemeToggle = useCallback(() => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setControlPanelTheme(newTheme);
  }, [currentTheme, setControlPanelTheme]);

  // 重置所有样式
  const handleResetStyles = useCallback(() => {
    resetToDefaults();
    resetStyles();
  }, [resetToDefaults, resetStyles]);

  // 渲染滑块控件
  const renderSlider = (
    label: string,
    value: number,
    min: number,
    max: number,
    step: number,
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void,
    unit?: string
  ) => (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        <span className="text-xs text-gray-500">{value}{unit}</span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={onChange}
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
      />
    </div>
  );

  // 渲染按钮组
  const renderButtonGroup = <T extends string>(
    label: string,
    options: readonly T[],
    currentValue: T,
    onChange: (value: T) => void,
    getIcon?: (value: T) => { icon: string; tooltip: string }
  ) => (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="grid grid-cols-3 gap-1">
        {options.map((option) => {
          const iconData = getIcon?.(option);
          const isActive = currentValue === option;
          return (
            <button
              key={option}
              onClick={() => onChange(option)}
              className={`px-3 py-2 text-xs font-medium rounded-md transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white border border-blue-500'
                  : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
              }`}
              title={iconData?.tooltip || option}
            >
              {iconData?.icon || option}
            </button>
          );
        })}
      </div>
    </div>
  );

  // 渲染开关控件
  const renderToggle = (
    label: string,
    checked: boolean,
    onChange: () => void,
    description?: string
  ) => (
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        {description && (
          <p className="text-xs text-gray-500 mt-1">{description}</p>
        )}
      </div>
      <button
        onClick={onChange}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          checked ? 'bg-blue-600' : 'bg-gray-200'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            checked ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 基础样式控制 */}
      <div className="space-y-4">
        <h3 className="text-sm font-semibold text-gray-800 border-b border-gray-200 pb-2">
          🎨 基础样式
        </h3>

        {/* 字体大小 */}
        {renderSlider('字体大小', fontSize, 8, 24, 1, handleFontSizeChange, 'px')}

        {/* 网格边距 */}
        {renderSlider('网格边距', matrixMargin, 0, 8, 1, handleMarginChange)}

        {/* 单元格形状 */}
        {renderButtonGroup(
          '单元格形状',
          getCellShapes(),
          cellShape,
          setCellShape,
          getShapeIcon
        )}

        {/* 显示模式 */}
        {renderButtonGroup(
          '显示模式',
          getDisplayModes(),
          displayMode,
          setDisplayMode,
          getDisplayModeIcon
        )}
      </div>

      {/* 高级样式控制 */}
      <div className="space-y-4">
        <h3 className="text-sm font-semibold text-gray-800 border-b border-gray-200 pb-2">
          ⚙️ 高级设置
        </h3>

        {/* 圆形缩放 */}
        {renderToggle(
          '圆形缩放效果',
          enableCircleScale,
          toggleCircleScale,
          '启用圆形单元格的缩放动画效果'
        )}

        {/* 圆形缩放因子 */}
        {enableCircleScale && renderSlider(
          '缩放因子',
          circleScaleFactor,
          1.0,
          2.0,
          0.1,
          handleScaleFactorChange,
          'x'
        )}

        {/* 虚拟化渲染 */}
        {renderToggle(
          '虚拟化渲染',
          enableVirtualization,
          toggleVirtualization,
          '启用虚拟化渲染以提升大网格性能'
        )}
      </div>

      {/* 显示控制 */}
      <div className="space-y-4">
        <h3 className="text-sm font-semibold text-gray-800 border-b border-gray-200 pb-2">
          👁️ 显示控制
        </h3>

        {/* 全局显示开关 */}
        {renderToggle(
          '显示所有数字',
          showAllNumbers,
          () => setShowAllNumbers(!showAllNumbers),
          '显示或隐藏所有单元格中的数字'
        )}

        {renderToggle(
          '显示所有颜色',
          showAllColors,
          () => setShowAllColors(!showAllColors),
          '显示或隐藏所有颜色的单元格'
        )}

        {/* 级别控制 */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-gray-700">级别显示控制</label>
          <div className="grid grid-cols-2 gap-2">
            {[
              { level: 1, show: showAllLevel1, toggle: setShowAllLevel1 },
              { level: 2, show: showAllLevel2, toggle: setShowAllLevel2 },
              { level: 3, show: showAllLevel3, toggle: setShowAllLevel3 },
              { level: 4, show: showAllLevel4, toggle: setShowAllLevel4 },
            ].map(({ level, show, toggle }) => (
              <button
                key={level}
                onClick={() => toggle(!show)}
                className={`px-3 py-2 text-xs font-medium rounded-md transition-all duration-200 ${
                  show
                    ? 'bg-green-600 text-white border border-green-500'
                    : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                }`}
              >
                级别 {level}
              </button>
            ))}
          </div>

          {/* 级别批量操作 */}
          <div className="grid grid-cols-2 gap-2 mt-2">
            <button
              onClick={() => toggleAllLevels(true)}
              className="px-3 py-2 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              全部显示
            </button>
            <button
              onClick={() => toggleAllLevels(false)}
              className="px-3 py-2 text-xs bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              全部隐藏
            </button>
          </div>
        </div>
      </div>

      {/* 主题控制 */}
      <div className="space-y-4">
        <h3 className="text-sm font-semibold text-gray-800 border-b border-gray-200 pb-2">
          🌙 主题设置
        </h3>

        {renderToggle(
          '深色主题',
          currentTheme === 'dark',
          handleThemeToggle,
          '切换浅色/深色主题'
        )}
      </div>

      {/* 操作按钮 */}
      <div className="space-y-3 pt-4 border-t border-gray-200">
        <button
          onClick={handleResetStyles}
          className="w-full px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          🔄 重置所有样式
        </button>

        <div className="text-xs text-gray-500 text-center">
          样式设置会自动保存
        </div>
      </div>
    </div>
  );
});

StylePanel.displayName = 'StylePanel';